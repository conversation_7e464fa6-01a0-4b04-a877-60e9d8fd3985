/**
 * 测试API响应处理
 */

// 模拟API响应数据
const mockApiResponse = {
  code: '0000',
  message: '请求失败', // 这个message可能是误导性的，但数据是正确的
  data: [
    {
      id: '1',
      name: '测试断面1',
      volt: '500KV',
      limit: '1000MW',
      maxValue: '800MW',
      maxDiffValue: '200MW',
      totalOverTime: '2小时',
      longestOverTime: '30分钟',
      maxValueTime: '2024-01-01 12:00:00',
    },
    {
      id: '2',
      name: '测试断面2',
      volt: '220KV',
      limit: '500MW',
      maxValue: '450MW',
      maxDiffValue: '50MW',
      totalOverTime: '1小时',
      longestOverTime: '15分钟',
      maxValueTime: '2024-01-01 14:00:00',
    },
  ],
}

/**
 * 测试响应处理逻辑
 */
export function testResponseHandling() {
  console.log('=== 测试API响应处理 ===')

  // 模拟响应处理逻辑
  const data = mockApiResponse

  if (data.code === 200 || data.code === '0000' || (data as any).success) {
    const processedResponse = {
      ...data,
      success: true,
    }

    console.log('✅ 响应处理成功:', processedResponse)
    console.log('数据条数:', processedResponse.data.length)

    return processedResponse
  } else {
    console.log('❌ 响应处理失败:', data)
    return null
  }
}

/**
 * 测试完整的API调用流程
 */
export async function testApiCall() {
  console.log('=== 测试完整API调用流程 ===')

  try {
    // 动态导入API服务
    const { SectionMonitoringService } = await import('./index')

    console.log('开始调用API...')
    const response = await SectionMonitoringService.getSectionStatisticList({
      startTime: '2024-01-01T00:00:00',
      endTime: '2024-12-31T23:59:59',
    })

    console.log('✅ API调用成功:', response)
    console.log('数据条数:', response.data?.length || 0)

    return response
  } catch (error) {
    console.error('❌ API调用失败:', error)
    return null
  }
}

// 如果在浏览器环境中，将测试函数挂载到全局对象
if (typeof window !== 'undefined') {
  ;(window as any).testResponseHandling = testResponseHandling
  ;(window as any).testApiCall = testApiCall
}
