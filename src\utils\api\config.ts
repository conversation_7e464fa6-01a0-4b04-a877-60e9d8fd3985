import type { ApiInstanceConfig } from '@/types/api'

/**
 * API配置
 */
export const apiConfig: ApiInstanceConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
}

/**
 * 开发环境配置
 */
export const devConfig: ApiInstanceConfig = {
  ...apiConfig,
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'
}

/**
 * 生产环境配置
 */
export const prodConfig: ApiInstanceConfig = {
  ...apiConfig,
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api'
}

/**
 * 根据环境获取配置
 */
export function getApiConfig(): ApiInstanceConfig {
  const env = import.meta.env.MODE
  
  switch (env) {
    case 'development':
      return devConfig
    case 'production':
      return prodConfig
    default:
      return apiConfig
  }
}

/**
 * API端点配置
 */
export const API_ENDPOINTS = {
  // 用户相关
  USER: {
    LOGIN: '/user/login',
    LOGOUT: '/user/logout',
    INFO: '/user/info',
    UPDATE: '/user/update'
  },
  
  // 现货市场相关
  SPOT_MARKET: {
    // 执行情况
    EXECUTION_SITUATION: '/spot-market/execution-situation',
    // 断面监测
    SECTION_MONITORING: '/spot-market/section-monitoring',
    // 数据统计
    STATISTICS: '/spot-market/statistics'
  },
  
  // 文件相关
  FILE: {
    UPLOAD: '/file/upload',
    DOWNLOAD: '/file/download'
  }
} as const

/**
 * 请求状态码
 */
export const HTTP_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  REQUEST_TIMEOUT: 408,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504
} as const

/**
 * 业务状态码
 */
export const BUSINESS_CODE = {
  SUCCESS: 200,
  FAIL: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500
} as const
