/**
 * API封装测试文件
 * 用于验证API封装是否正常工作
 */

import api, { UserService, SpotMarketService, FileService } from './index'
import type { ApiResponse } from '@/types/api'

/**
 * 测试API基础功能
 */
export async function testApiBasics() {
  console.log('=== 测试API基础功能 ===')
  
  try {
    // 测试GET请求（这里使用一个模拟的端点）
    console.log('测试GET请求...')
    // const getResponse = await api.get('/test')
    // console.log('GET响应:', getResponse)
    
    // 测试POST请求
    console.log('测试POST请求...')
    // const postResponse = await api.post('/test', { message: 'Hello API' })
    // console.log('POST响应:', postResponse)
    
    console.log('✅ API基础功能测试完成')
  } catch (error) {
    console.error('❌ API基础功能测试失败:', error)
  }
}

/**
 * 测试用户服务
 */
export async function testUserService() {
  console.log('=== 测试用户服务 ===')
  
  try {
    // 测试登录（模拟）
    console.log('测试用户登录...')
    // const loginResponse = await UserService.login({
    //   username: 'test',
    //   password: 'test123'
    // })
    // console.log('登录响应:', loginResponse)
    
    console.log('✅ 用户服务测试完成')
  } catch (error) {
    console.error('❌ 用户服务测试失败:', error)
  }
}

/**
 * 测试现货市场服务
 */
export async function testSpotMarketService() {
  console.log('=== 测试现货市场服务 ===')
  
  try {
    // 测试获取执行情况列表（模拟）
    console.log('测试获取执行情况列表...')
    // const executionList = await SpotMarketService.getExecutionSituationList({
    //   page: 1,
    //   pageSize: 10
    // })
    // console.log('执行情况列表:', executionList)
    
    console.log('✅ 现货市场服务测试完成')
  } catch (error) {
    console.error('❌ 现货市场服务测试失败:', error)
  }
}

/**
 * 测试文件服务
 */
export async function testFileService() {
  console.log('=== 测试文件服务 ===')
  
  try {
    // 测试文件上传（需要实际文件）
    console.log('文件服务类型检查通过')
    
    console.log('✅ 文件服务测试完成')
  } catch (error) {
    console.error('❌ 文件服务测试失败:', error)
  }
}

/**
 * 测试错误处理
 */
export async function testErrorHandling() {
  console.log('=== 测试错误处理 ===')
  
  try {
    // 测试404错误
    console.log('测试404错误处理...')
    // await api.get('/non-existent-endpoint')
  } catch (error: any) {
    console.log('✅ 错误处理正常:', error.message)
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行API封装测试...\n')
  
  await testApiBasics()
  console.log('')
  
  await testUserService()
  console.log('')
  
  await testSpotMarketService()
  console.log('')
  
  await testFileService()
  console.log('')
  
  await testErrorHandling()
  console.log('')
  
  console.log('🎉 所有测试完成！')
}

/**
 * 验证类型定义
 */
export function validateTypes() {
  console.log('=== 验证类型定义 ===')
  
  // 验证API响应类型
  const mockResponse: ApiResponse<string> = {
    code: 200,
    message: 'success',
    data: 'test data',
    success: true,
    timestamp: Date.now()
  }
  
  console.log('✅ API响应类型验证通过')
  
  // 验证请求配置类型
  const mockConfig = {
    showLoading: true,
    showError: false,
    timeout: 5000,
    retry: true,
    retryCount: 3
  }
  
  console.log('✅ 请求配置类型验证通过')
  
  console.log('✅ 所有类型定义验证通过')
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  console.log('API封装已加载，可以在控制台中运行测试:')
  console.log('- runAllTests() // 运行所有测试')
  console.log('- validateTypes() // 验证类型定义')
  
  // 将测试函数挂载到全局对象上，方便在控制台中调用
  ;(window as any).apiTests = {
    runAllTests,
    validateTypes,
    testApiBasics,
    testUserService,
    testSpotMarketService,
    testFileService,
    testErrorHandling
  }
}
