import axios, { type AxiosInstance, type AxiosResponse, type AxiosError } from 'axios'
import type {
  ApiResponse,
  PaginatedResponse,
  RequestConfig,
  ApiInstanceConfig,
  ApiError,
  HttpMethod,
} from '@/types/api'

/**
 * HTTP请求类
 */
export class HttpRequest {
  private instance: AxiosInstance
  private defaultConfig: RequestConfig = {
    showLoading: false,
    showError: true,
    retry: false,
    retryCount: 3,
  }

  constructor(config: ApiInstanceConfig = {}) {
    // 创建axios实例
    this.instance = axios.create({
      baseURL: config.baseURL || import.meta.env.VITE_API_BASE_URL || '/api',
      timeout: config.timeout || 10000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    })

    // 设置请求拦截器
    this.setupRequestInterceptors(config.requestInterceptors)
    // 设置响应拦截器
    this.setupResponseInterceptors(config.responseInterceptors)
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptors(interceptors?: any[]) {
    // 默认请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加token
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加时间戳防止缓存
        if (config.method === 'get') {
          config.params = {
            ...config.params,
            _t: Date.now(),
          }
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      },
    )

    // 自定义拦截器
    if (interceptors && interceptors.length > 0) {
      interceptors.forEach((interceptor) => {
        this.instance.interceptors.request.use(interceptor.onFulfilled, interceptor.onRejected)
      })
    }
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptors(interceptors?: any[]) {
    // 默认响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        const { data } = response

        // 如果是文件下载等特殊情况，直接返回
        if (response.config.responseType === 'blob') {
          return response as any
        }

        // 统一处理响应数据
        if (data.code === 200 || data.code === '0000' || data.success) {
          return {
            ...data,
            success: true,
          }
        } else {
          // 业务错误
          const error: ApiError = {
            code: data.code,
            message: data.message || '请求失败',
            data: data.data,
          }
          return Promise.reject(error)
        }
      },
      (error: AxiosError) => {
        return this.handleError(error)
      },
    )

    // 自定义拦截器
    if (interceptors && interceptors.length > 0) {
      interceptors.forEach((interceptor) => {
        this.instance.interceptors.response.use(interceptor.onFulfilled, interceptor.onRejected)
      })
    }
  }

  /**
   * 错误处理
   */
  private handleError(error: AxiosError): Promise<ApiError> {
    const apiError: ApiError = {
      code: error.response?.status || 0,
      message: '网络错误',
      status: error.response?.status,
      statusText: error.response?.statusText,
    }

    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      apiError.message = (data as any)?.message || this.getErrorMessage(status)
      apiError.data = data
    } else if (error.request) {
      // 网络错误
      apiError.message = '网络连接失败，请检查网络设置'
    } else {
      // 其他错误
      apiError.message = error.message || '请求失败'
    }

    return Promise.reject(apiError)
  }

  /**
   * 根据状态码获取错误信息
   */
  private getErrorMessage(status: number): string {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '拒绝访问',
      404: '请求的资源不存在',
      405: '请求方法不允许',
      408: '请求超时',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    }
    return messages[status] || `请求失败 (${status})`
  }

  /**
   * 获取token
   */
  private getToken(): string | null {
    return localStorage.getItem('token') || sessionStorage.getItem('token')
  }

  /**
   * 通用请求方法
   */
  public async request<T = any>(
    method: HttpMethod,
    url: string,
    data?: any,
    config: RequestConfig = {},
  ): Promise<ApiResponse<T>> {
    const mergedConfig = { ...this.defaultConfig, ...config }
    try {
      const response = await this.instance.request({
        method: method.toLowerCase(),
        url,
        ...(method === 'GET' ? { params: data } : { data }),
        ...mergedConfig,
      })

      // 返回响应数据，axios响应拦截器已经处理了数据格式
      return response.data as ApiResponse<T>
    } catch (error) {
      if (mergedConfig.customErrorHandler) {
        mergedConfig.customErrorHandler(error)
      }
      throw error
    }
  }

  /**
   * GET请求
   */
  public get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('GET', url, params, config)
  }

  /**
   * POST请求
   */
  public post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('POST', url, data, config)
  }

  /**
   * PUT请求
   */
  public put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', url, data, config)
  }

  /**
   * DELETE请求
   */
  public delete<T = any>(
    url: string,
    params?: any,
    config?: RequestConfig,
  ): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', url, params, config)
  }

  /**
   * PATCH请求
   */
  public patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', url, data, config)
  }

  /**
   * 分页请求
   */
  public async getPaginated<T = any>(
    url: string,
    params?: any,
    config?: RequestConfig,
  ): Promise<PaginatedResponse<T>> {
    return this.get<PaginatedResponse<T>['data']>(url, params, config)
  }

  /**
   * 文件上传
   */
  public upload<T = any>(
    url: string,
    file: File | FormData,
    config?: RequestConfig,
  ): Promise<ApiResponse<T>> {
    const formData = file instanceof FormData ? file : new FormData()
    if (file instanceof File) {
      formData.append('file', file)
    }

    return this.post<T>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
    })
  }

  /**
   * 文件下载
   */
  public async download(
    url: string,
    params?: any,
    filename?: string,
    config?: RequestConfig,
  ): Promise<void> {
    const response = await this.instance.get(url, {
      ...config,
      params,
      responseType: 'blob',
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  }
}
