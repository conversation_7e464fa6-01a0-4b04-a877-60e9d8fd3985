<template>
  <div class="flex px-7.5 py-4 items-center bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <span class="text-2xl text-#9E9E9E mr-9">查询结果</span>

    <div class="flex flex-1">
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          type="datetime"
          size="large"
          placeholder="请选择时间"
          clearable
        >
        </n-date-picker>
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <n-button size="large" class="mr-2.5">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出表格
      </n-button>

      <n-button type="info" size="large" @click="handleBack">
        <template #icon>
          <n-icon><ChevronLeft20FilledIcon /></n-icon>
        </template>
        返回
      </n-button>
    </div>
  </div>
  <LineChart title="断面潮流曲线" :data="multiSeriesData" height="350px" class="mt-2" />

  <SectionDetail></SectionDetail>
  <DataTable :columns="columns" :data="tableData" height="calc(100vh - 670px)"> </DataTable>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NDatePicker, NInput, NButton, NSelect, NIcon, type SelectOption } from 'naive-ui'
import InputGroup from '@/components/shared/InputGroup.vue'

import { SearchIcon, ExportIcon, ChevronLeft20FilledIcon } from '@/utils/constant/icons'

import LineChart from '@/components/shared/charts/LineChart.vue'
import DataTable from '@/components/shared/DataTable.vue'
import SectionDetail from './SectionDetail.vue'
import { useSectionMonitoringStore } from '@/stores'

// 多系列数据
const multiSeriesData = ref([
  {
    name: '未来曲线',
    data: [
      { name: '1月', value: 820 },
      { name: '2月', value: 932 },
      { name: '3月', value: 901 },
      { name: '4月', value: 934 },
      { name: '5月', value: 1290 },
      { name: '6月', value: 1330 },
    ],
  },
  {
    name: '今日曲线',
    data: [
      { name: '1月', value: 720 },
      { name: '2月', value: 832 },
      { name: '3月', value: 801 },
      { name: '4月', value: 834 },
      { name: '5月', value: 1190 },
      { name: '6月', value: 1230 },
    ],
  },
  {
    name: '今日限制',
    data: [
      { name: '1月', value: 1000 },
      { name: '2月', value: 1000 },
      { name: '3月', value: 1000 },
      { name: '4月', value: 1000 },
      { name: '5月', value: 1000 },
      { name: '6月', value: 1000 },
    ],
    color: 'rgba(230, 176, 46, 1)',
    gradientColors: {
      start: 'rgba(230, 176, 46, 0)',
      end: 'rgba(230, 176, 46, 0)',
    },
  },
])

// 定义表格列配置
const columns = ref([
  {
    key: 'lineName',
    title: '线路名称',
    sortable: true,
    align: 'left' as const,
  },
  {
    key: 'voltageLevel',
    title: '电压等级',
    sortable: true,
    align: 'center' as const,
  },
  {
    key: 'action',
    title: '详情',
    sortable: false,
    width: '10%',
    align: 'center' as const,
  },
])

// 定义表格数据
const tableData = ref([
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
])

const sectionMonitoringStore = useSectionMonitoringStore()

const handleBack = () => {
  sectionMonitoringStore.selectedTableRow = null
}
</script>
