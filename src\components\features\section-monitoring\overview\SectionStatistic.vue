<template>
  <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <div class="flex flex-1">
      <InputGroup size="large" label="时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          v-model:value="timestamp"
          type="datetime"
          size="large"
          placeholder="请选择时间"
          clearable
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="名称" class="mr-2.5 max-w-317px">
        <n-input size="large" placeholder="请输入名称" clearable> </n-input>
      </InputGroup>

      <InputGroup size="large" label="电压" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="volt"
          class="w-full"
          placeholder=""
          :options="voltOptions"
          size="large"
          clearable
        />
      </InputGroup>

      <InputGroup size="large" label="状态" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="status"
          size="large"
          placeholder=""
          :options="statusOptions"
          clearable
        />
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <n-button size="large">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出表格
      </n-button>
    </div>
  </div>

  <DataTable :columns="columns" :data="tableData" height="calc(100vh - 170px)" @sort="handleSort">
    <template #action="{ item, index }">
      <n-button type="primary" @click="handleViewDetail(item as TableRowData, index)">
        查看
      </n-button>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NDatePicker, NInput, NButton, NSelect, NIcon, type SelectOption } from 'naive-ui'
import DataTable from '@/components/shared/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'

import { SearchIcon, ExportIcon } from '@/utils/constant/icons'

import { useExecutionSituationStore } from '@/stores/'

// 定义数据类型
interface TableRowData {
  lineName: string
  voltageLevel: string
  powerFlow: string
  maxPowerFlow: string
  branchCongestion: string
  marginalPrice: string
  operationDuration: string
  maxFlowTime: string
  isHighlighted: boolean
}

// 定义表格列配置
const columns = ref([
  {
    key: 'lineName',
    title: '线路名称',
    sortable: true,
    align: 'left' as const,
  },
  {
    key: 'voltageLevel',
    title: '电压等级',
    sortable: true,
    align: 'center' as const,
  },
  {
    key: 'action',
    title: '详情',
    sortable: false,
    width: '10%',
    align: 'center' as const,
  },
])

// 定义表格数据
const tableData = ref([
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '雷溪5k27线，杰溪5k28线双线',
    voltageLevel: '200kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
  {
    lineName: '月溪5k11线，月溪5k12线双线',
    voltageLevel: '500kV',
    powerFlow: 'XXX',
    maxPowerFlow: 'XXX',
    branchCongestion: 'XXX',
    marginalPrice: 'XXX',
    operationDuration: 'XXX',
    maxFlowTime: '2025-01-01 22:12:34',
  },
])

const executionSituationStore = useExecutionSituationStore()

const timestamp = ref(null)
const volt = ref(null)
const status = ref(null)

const voltOptions: SelectOption[] = [
  {
    label: '500kV',
    value: '500kV',
  },
  {
    label: '220kV',
    value: '220kV',
  },
]

const statusOptions: SelectOption[] = [
  {
    label: '越限',
    value: '越限',
  },
  {
    label: '重载',
    value: '重载',
  },
]

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null) => {
  console.log('排序:', column.key, order)

  if (!order) {
    // 恢复原始顺序
    return
  }

  tableData.value.sort((a, b) => {
    const aValue = (a as any)[column.key]
    const bValue = (b as any)[column.key]

    if (order === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })
}

// 查看详情处理函数
const handleViewDetail = (item: TableRowData, index: number) => {
  console.log('查看详情:', item, index)
  executionSituationStore.selectedTableRow = item
  // 这里可以添加查看详情的逻辑
}
</script>
<style></style>
