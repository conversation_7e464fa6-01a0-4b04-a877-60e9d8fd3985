<template>
  <div class="flex px-9 py-4 bg-#F8F8F8 border-b-1 border-b-#D7D7D7 border-b-solid">
    <div class="flex flex-1">
      <InputGroup size="large" label="开始时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          v-model:value="formData.startTime"
          type="datetime"
          size="large"
          placeholder="请选择开始时间"
          clearable
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="结束时间" class="mr-2.5 max-w-317px">
        <n-date-picker
          class="w-full"
          v-model:value="formData.endTime"
          type="datetime"
          size="large"
          placeholder="请选择结束时间"
          clearable
        >
        </n-date-picker>
      </InputGroup>

      <InputGroup size="large" label="名称" class="mr-2.5 max-w-317px">
        <n-input v-model:value="formData.name" size="large" placeholder="请输入名称" clearable>
        </n-input>
      </InputGroup>

      <InputGroup size="large" label="电压" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.volt"
          class="w-full"
          placeholder=""
          :options="voltOptions"
          size="large"
          clearable
        />
      </InputGroup>

      <InputGroup size="large" label="状态" class="mr-2.5 max-w-212px">
        <n-select
          v-model:value="formData.status"
          size="large"
          placeholder=""
          :options="statusOptions"
          clearable
        />
      </InputGroup>
    </div>
    <div class="flex">
      <n-button type="info" class="mr-2.5" size="large" @click="handleSearch">
        <template #icon>
          <n-icon><SearchIcon /></n-icon>
        </template>
        搜索
      </n-button>
      <n-button size="large" @click="handleExport">
        <template #icon>
          <n-icon><ExportIcon /></n-icon>
        </template>
        导出表格
      </n-button>
    </div>
  </div>

  <DataTable
    :columns="columns"
    :data="tableData"
    :loading="loading"
    height="calc(100vh - 170px)"
    @sort="handleSort"
  >
    <template #action="{ item, index }">
      <n-button type="primary" @click="handleViewDetail(item as TableRowData, index)">
        查看
      </n-button>
    </template>
  </DataTable>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { NDatePicker, NInput, NButton, NSelect, NIcon, type SelectOption } from 'naive-ui'
import DataTable from '@/components/shared/DataTable.vue'
import InputGroup from '@/components/shared/InputGroup.vue'

import { SearchIcon, ExportIcon } from '@/utils/constant/icons'
import {
  SpotMarketService,
  type SectionStatisticData,
  type SectionStatisticQueryParams,
} from '@/utils/api'

import { useExecutionSituationStore } from '@/stores/'

// 定义数据类型
interface TableRowData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

// 定义表格列配置
const columns = ref([
  {
    key: 'name',
    title: '断面名称',
    sortable: false,
    align: 'left' as const,
    width: '20%',
  },
  {
    key: 'volt',
    title: '电压等级',
    sortable: false,
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'limit',
    title: '限额',
    sortable: false,
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'maxValue',
    title: '最大潮流',
    sortable: true,
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'maxDiffValue',
    title: '差额最大值',
    sortable: true,
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'totalOverTime',
    title: '总越限时间',
    sortable: true,
    align: 'center' as const,
    width: '10%',
  },
  {
    key: 'longestOverTime',
    title: '最长出现越线时长',
    sortable: true,
    align: 'center' as const,
    width: '12%',
  },
  {
    key: 'maxValueTime',
    title: '最大潮流越线出现时间',
    sortable: false,
    align: 'center' as const,
    width: '13%',
  },
  {
    key: 'action',
    title: '详情',
    sortable: false,
    width: '5%',
    align: 'center' as const,
  },
])

// 定义表格数据
const tableData = ref<TableRowData[]>([])

// 定义加载状态
const loading = ref(false)

// 定义响应式变量
const executionSituationStore = useExecutionSituationStore()

// 表单数据
const formData = ref({
  startTime: null as number | null,
  endTime: null as number | null,
  name: '',
  volt: null as string | null,
  status: null as string | null,
})

// 选项配置
const voltOptions: SelectOption[] = [
  {
    label: '500kV',
    value: '500kV',
  },
  {
    label: '220kV',
    value: '220kV',
  },
]

const statusOptions: SelectOption[] = [
  {
    label: '越限',
    value: '越限',
  },
  {
    label: '重载',
    value: '重载',
  },
]

// 获取断面统计数据
const fetchSectionStatisticList = async () => {
  loading.value = true
  try {
    const params: SectionStatisticQueryParams = {}

    // 构建查询参数
    if (formData.value.startTime) {
      params.startTime = new Date(formData.value.startTime).toISOString()
    }
    if (formData.value.endTime) {
      params.endTime = new Date(formData.value.endTime).toISOString()
    }
    if (formData.value.name) {
      params.name = formData.value.name
    }
    if (formData.value.volt) {
      params.volt = formData.value.volt
    }
    if (formData.value.status) {
      params.status = formData.value.status
    }

    const response = await SpotMarketService.getSectionStatisticList(params)

    if (response.success && response.data) {
      tableData.value = response.data.map((item: SectionStatisticData) => ({
        id: item.id,
        name: item.name,
        volt: item.volt,
        limit: item.limit,
        maxValue: item.maxValue,
        maxDiffValue: item.maxDiffValue,
        totalOverTime: item.totalOverTime,
        longestOverTime: item.longestOverTime,
        maxValueTime: item.maxValueTime,
      }))
    }
  } catch (error) {
    console.error('获取断面统计数据失败:', error)
    // 这里可以添加错误提示
  } finally {
    loading.value = false
  }
}

// 搜索处理函数
const handleSearch = () => {
  fetchSectionStatisticList()
}

// 排序处理函数
const handleSort = (column: any, order: 'asc' | 'desc' | null) => {
  console.log('排序:', column.key, order)

  if (!order) {
    // 恢复原始顺序
    fetchSectionStatisticList()
    return
  }

  tableData.value.sort((a, b) => {
    const aValue = parseFloat((a as any)[column.key]) || 0
    const bValue = parseFloat((b as any)[column.key]) || 0

    if (order === 'asc') {
      return aValue - bValue
    } else {
      return bValue - aValue
    }
  })
}

// 查看详情处理函数
const handleViewDetail = (item: TableRowData, index: number) => {
  console.log('查看详情:', item, index)
  executionSituationStore.selectedTableRow = item
  // 这里可以添加查看详情的逻辑，比如打开详情弹窗或跳转到详情页面
}

// 导出表格处理函数
const handleExport = () => {
  console.log('导出表格')
  // 这里可以添加导出逻辑
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSectionStatisticList()
})
</script>
<style></style>
