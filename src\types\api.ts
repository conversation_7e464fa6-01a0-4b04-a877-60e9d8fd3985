import type { AxiosRequestConfig, AxiosResponse } from 'axios'

// 基础响应结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
  timestamp?: number
}

// 分页响应结构
export interface PaginatedResponse<T = any> {
  code: number
  message: string
  data: {
    list: T[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
  success: boolean
  timestamp?: number
}

// 请求配置扩展
export interface RequestConfig extends AxiosRequestConfig {
  // 是否显示loading
  showLoading?: boolean
  // 是否显示错误提示
  showError?: boolean
  // 是否重试
  retry?: boolean
  // 重试次数
  retryCount?: number
  // 自定义错误处理
  customErrorHandler?: (error: any) => void
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求拦截器配置
export interface RequestInterceptorConfig {
  onFulfilled?: (config: any) => any
  onRejected?: (error: any) => any
}

// 响应拦截器配置
export interface ResponseInterceptorConfig {
  onFulfilled?: (response: AxiosResponse) => any
  onRejected?: (error: any) => any
}

// API实例配置
export interface ApiInstanceConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
  requestInterceptors?: RequestInterceptorConfig[]
  responseInterceptors?: ResponseInterceptorConfig[]
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  data?: any
  status?: number
  statusText?: string
}
