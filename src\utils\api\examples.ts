/**
 * API使用示例
 * 这个文件展示了如何使用封装的API
 */

import api, { UserService, SpotMarketService, FileService } from './index'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

/**
 * 基础API使用示例
 */
export class ApiExamples {
  /**
   * 基础GET请求示例
   */
  static async basicGetExample() {
    try {
      const response = await api.get('/test')
      console.log('GET Response:', response)
    } catch (error) {
      console.error('GET Error:', error)
    }
  }

  /**
   * 基础POST请求示例
   */
  static async basicPostExample() {
    try {
      const response = await api.post('/test', {
        name: 'test',
        value: 123,
      })
      console.log('POST Response:', response)
    } catch (error) {
      console.error('POST Error:', error)
    }
  }

  /**
   * 带配置的请求示例
   */
  static async requestWithConfigExample() {
    try {
      const response = await api.get(
        '/test',
        {},
        {
          showLoading: true,
          showError: false,
          timeout: 5000,
        },
      )
      console.log('Response with config:', response)
    } catch (error) {
      console.error('Request Error:', error)
    }
  }

  /**
   * 用户服务使用示例
   */
  static async userServiceExample() {
    try {
      // 登录
      const loginResponse = await UserService.login({
        username: 'admin',
        password: '123456',
      })
      console.log('Login Response:', loginResponse)

      // 获取用户信息
      const userInfo = await UserService.getUserInfo()
      console.log('User Info:', userInfo)

      // 更新用户信息
      const updateResponse = await UserService.updateUserInfo({
        username: 'newUsername',
        email: '<EMAIL>',
      })
      console.log('Update Response:', updateResponse)
    } catch (error) {
      console.error('User Service Error:', error)
    }
  }

  /**
   * 现货市场服务使用示例
   */
  static async spotMarketServiceExample() {
    try {
      // 获取执行情况列表
      const executionList = await SpotMarketService.getExecutionSituationList({
        page: 1,
        pageSize: 10,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      })
      console.log('Execution List:', executionList)

      // 获取断面监测数据
      const monitoringList = await SpotMarketService.getSectionMonitoringList({
        page: 1,
        pageSize: 20,
      })
      console.log('Monitoring List:', monitoringList)

      // 获取统计数据
      const statistics = await SpotMarketService.getStatistics({
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      })
      console.log('Statistics:', statistics)

      // 导出数据
      await SpotMarketService.exportExecutionSituation(
        {
          startDate: '2024-01-01',
          endDate: '2024-12-31',
        },
        'execution_data.xlsx',
      )
    } catch (error) {
      console.error('Spot Market Service Error:', error)
    }
  }

  /**
   * 文件服务使用示例
   */
  static async fileServiceExample() {
    try {
      // 假设有一个文件输入元素
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
      const file = fileInput?.files?.[0]

      if (file) {
        // 单文件上传
        const uploadResponse = await FileService.uploadFile(file)
        console.log('Upload Response:', uploadResponse)

        // 获取文件信息
        const fileInfo = await FileService.getFileInfo(uploadResponse.data.file.id)
        console.log('File Info:', fileInfo)

        // 下载文件
        await FileService.downloadFile(uploadResponse.data.file.id, 'downloaded_file.txt')
      }

      // 多文件上传
      const files = Array.from(fileInput?.files || [])
      if (files.length > 0) {
        const batchUploadResponse = await FileService.uploadFiles(files)
        console.log('Batch Upload Response:', batchUploadResponse)
      }
    } catch (error) {
      console.error('File Service Error:', error)
    }
  }

  /**
   * 分页请求示例
   */
  static async paginationExample() {
    try {
      const response: PaginatedResponse<any> = await api.getPaginated('/data', {
        page: 1,
        pageSize: 10,
        keyword: 'search',
      })

      console.log('Total:', response.data.total)
      console.log('Current Page:', response.data.page)
      console.log('Page Size:', response.data.pageSize)
      console.log('Total Pages:', response.data.totalPages)
      console.log('Data List:', response.data.list)
    } catch (error) {
      console.error('Pagination Error:', error)
    }
  }

  /**
   * 错误处理示例
   */
  static async errorHandlingExample() {
    try {
      const response = await api.get('/non-existent-endpoint')
      console.log('Response:', response)
    } catch (error: any) {
      // 统一的错误处理
      if (error.code === 401) {
        console.log('需要重新登录')
        // 跳转到登录页
      } else if (error.code === 403) {
        console.log('没有权限')
        // 显示权限不足提示
      } else if (error.code === 404) {
        console.log('资源不存在')
        // 显示404页面
      } else {
        console.log('请求失败:', error.message)
        // 显示通用错误提示
      }
    }
  }

  /**
   * 自定义错误处理示例
   */
  static async customErrorHandlerExample() {
    try {
      const response = await api.get(
        '/test',
        {},
        {
          customErrorHandler: (error: any) => {
            console.log('自定义错误处理:', error)
            // 自定义错误处理逻辑
          },
        },
      )
      console.log('Response:', response)
    } catch (error) {
      // 这里不会执行，因为使用了自定义错误处理
      console.error('Default Error Handler:', error)
    }
  }
}

/**
 * Vue组合式API使用示例
 */
export function useApiExample() {
  // 注意：在实际使用中需要从 'vue' 导入 ref
  // import { ref } from 'vue'
  const loading = { value: false }
  const error = { value: null as string | null }
  const data = { value: null as any }

  const fetchData = async (url: string, params?: any) => {
    loading.value = true
    error.value = null

    try {
      const response = await api.get(url, params)
      data.value = response.data
    } catch (err: any) {
      error.value = err.message
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    data,
    fetchData,
  }
}

// 注意：这个文件仅用于示例，实际项目中可以删除
