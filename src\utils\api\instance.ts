import { HttpRequest } from './request'
import { getApiConfig } from './config'
import type { RequestInterceptorConfig, ResponseInterceptorConfig } from '@/types/api'

/**
 * 请求拦截器配置
 */
const requestInterceptors: RequestInterceptorConfig[] = [
  {
    onFulfilled: (config) => {
      // 可以在这里添加全局的请求处理逻辑
      console.log('Request:', config.method?.toUpperCase(), config.url)
      return config
    },
    onRejected: (error) => {
      console.error('Request Error:', error)
      return Promise.reject(error)
    }
  }
]

/**
 * 响应拦截器配置
 */
const responseInterceptors: ResponseInterceptorConfig[] = [
  {
    onFulfilled: (response) => {
      // 可以在这里添加全局的响应处理逻辑
      console.log('Response:', response.status, response.config.url)
      return response
    },
    onRejected: (error) => {
      console.error('Response Error:', error)
      
      // 全局错误处理
      if (error.code === 401) {
        // 未授权，跳转到登录页
        // router.push('/login')
        console.warn('Unauthorized, please login again')
      }
      
      return Promise.reject(error)
    }
  }
]

/**
 * 创建API实例
 */
export const api = new HttpRequest({
  ...getApiConfig(),
  requestInterceptors,
  responseInterceptors
})

/**
 * 创建不带拦截器的简单实例
 */
export const simpleApi = new HttpRequest(getApiConfig())

export default api
