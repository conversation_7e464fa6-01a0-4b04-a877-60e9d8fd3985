import api from '../instance'
import { API_ENDPOINTS } from '../config'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

/**
 * 执行情况数据类型
 */
export interface ExecutionSituationData {
  id: string
  date: string
  time: string
  marketType: string
  executionStatus: string
  totalVolume: number
  totalAmount: number
  avgPrice: number
  details: ExecutionDetail[]
}

/**
 * 执行详情
 */
export interface ExecutionDetail {
  id: string
  region: string
  volume: number
  amount: number
  price: number
  status: string
}

/**
 * 断面监测数据类型
 */
export interface SectionMonitoringData {
  id: string
  sectionName: string
  monitorTime: string
  currentLoad: number
  maxLoad: number
  loadRate: number
  status: 'normal' | 'warning' | 'alarm'
  voltage: number
  frequency: number
}

/**
 * 断面统计数据类型
 */
export interface SectionStatisticData {
  id: string
  name: string
  volt: string
  limit: string
  maxValue: string
  maxDiffValue: string
  totalOverTime: string
  longestOverTime: string
  maxValueTime: string
}

/**
 * 统计数据类型
 */
export interface StatisticsData {
  totalVolume: number
  totalAmount: number
  avgPrice: number
  marketCount: number
  successRate: number
  trends: TrendData[]
}

/**
 * 趋势数据
 */
export interface TrendData {
  date: string
  volume: number
  amount: number
  price: number
}

/**
 * 查询参数
 */
export interface QueryParams {
  startDate?: string
  endDate?: string
  marketType?: string
  status?: string
  page?: number
  pageSize?: number
}

/**
 * 断面统计查询参数
 */
export interface SectionStatisticQueryParams {
  startTime?: string
  endTime?: string
  name?: string
  volt?: string
  status?: string
}

/**
 * 现货市场相关API
 */
export class SpotMarketService {
  /**
   * 获取执行情况列表
   */
  static async getExecutionSituationList(
    params?: QueryParams,
  ): Promise<PaginatedResponse<ExecutionSituationData>> {
    return api.getPaginated<ExecutionSituationData>(
      API_ENDPOINTS.SPOT_MARKET.EXECUTION_SITUATION,
      params,
    )
  }

  /**
   * 获取执行情况详情
   */
  static async getExecutionSituationDetail(
    id: string,
  ): Promise<ApiResponse<ExecutionSituationData>> {
    return api.get<ExecutionSituationData>(`${API_ENDPOINTS.SPOT_MARKET.EXECUTION_SITUATION}/${id}`)
  }

  /**
   * 获取断面监测列表
   */
  static async getSectionMonitoringList(
    params?: QueryParams,
  ): Promise<PaginatedResponse<SectionMonitoringData>> {
    return api.getPaginated<SectionMonitoringData>(
      API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING,
      params,
    )
  }

  /**
   * 获取断面监测详情
   */
  static async getSectionMonitoringDetail(id: string): Promise<ApiResponse<SectionMonitoringData>> {
    return api.get<SectionMonitoringData>(`${API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING}/${id}`)
  }

  /**
   * 获取断面统计列表
   */
  static async getSectionStatisticList(
    params?: SectionStatisticQueryParams,
  ): Promise<ApiResponse<SectionStatisticData[]>> {
    return api.get<SectionStatisticData[]>(API_ENDPOINTS.SPOT_MARKET.SECTION_STATISTIC_LIST, params)
  }

  /**
   * 获取统计数据
   */
  static async getStatistics(params?: QueryParams): Promise<ApiResponse<StatisticsData>> {
    return api.get<StatisticsData>(API_ENDPOINTS.SPOT_MARKET.STATISTICS, params)
  }

  /**
   * 导出执行情况数据
   */
  static async exportExecutionSituation(params?: QueryParams, filename?: string): Promise<void> {
    return api.download(
      `${API_ENDPOINTS.SPOT_MARKET.EXECUTION_SITUATION}/export`,
      params,
      filename || `execution_situation_${Date.now()}.xlsx`,
    )
  }

  /**
   * 导出断面监测数据
   */
  static async exportSectionMonitoring(params?: QueryParams, filename?: string): Promise<void> {
    return api.download(
      `${API_ENDPOINTS.SPOT_MARKET.SECTION_MONITORING}/export`,
      params,
      filename || `section_monitoring_${Date.now()}.xlsx`,
    )
  }
}

export default SpotMarketService
